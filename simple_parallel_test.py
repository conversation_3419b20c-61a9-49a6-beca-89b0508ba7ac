#!/usr/bin/env python3
"""
简化的并行采样测试
"""

import os
import sys
import time

# 设置环境变量
os.environ["JAX_PLATFORM_NAME"] = "cpu"  # 使用CPU进行测试
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"

import jax
import jax.numpy as jnp
import netket as nk
import numpy as np

def log_message(log_file, message):
    """简化的日志函数"""
    print(message)

# 真正的GPU并行采样函数（简化版）
def simple_parallel_expectation(vqs, operators):
    """
    简化的并行期望值计算
    """
    if len(operators) == 0:
        return []
    
    if len(operators) == 1:
        return [vqs.expect(operators[0])]
    
    print(f"并行计算 {len(operators)} 个算符的期望值...")
    
    # 生成共享样本
    samples = vqs.samples
    log_psi = vqs.log_value(samples)
    
    results = []
    
    # 批量处理算符
    batch_size = min(4, len(operators))
    
    for i in range(0, len(operators), batch_size):
        batch_ops = operators[i:i+batch_size]
        batch_results = []
        
        for operator in batch_ops:
            try:
                # 使用NetKet的标准方法计算局域能量
                local_energies = operator.get_local_kernel(samples, vqs.log_value)

                # 计算统计量
                mean_val = jnp.mean(local_energies)
                variance_val = jnp.var(local_energies)
                error_val = jnp.sqrt(variance_val / samples.shape[0])

                # 创建统计对象
                stats = nk.stats.Stats(
                    mean=mean_val,
                    error_of_mean=error_val,
                    variance=variance_val
                )

                batch_results.append(stats)

            except Exception as e:
                print(f"算符计算出错，回退到标准方法: {e}")
                stats = vqs.expect(operator)
                batch_results.append(stats)
        
        results.extend(batch_results)
        print(f"完成 {min(i + batch_size, len(operators))}/{len(operators)} 个算符")
    
    return results

def create_test_system(L=4):
    """创建测试系统"""
    lattice = nk.graph.Square(L, pbc=True)
    hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes)
    
    model = nk.models.RBM(alpha=1, param_dtype=complex)
    sampler = nk.sampler.MetropolisLocal(hilbert)
    
    vqs = nk.vqs.MCState(
        sampler=sampler,
        model=model,
        n_samples=512,
        n_discard_per_chain=0,
        chunk_size=128
    )
    
    return lattice, hilbert, vqs

def create_test_operators(hilbert, n_ops=6):
    """创建测试算符"""
    operators = []
    N = hilbert.size
    
    for i in range(min(n_ops, N)):
        j = (i + 1) % N
        
        # S_i · S_j 算符
        sx_i = nk.operator.spin.sigmax(hilbert, i) * 0.5
        sy_i = nk.operator.spin.sigmay(hilbert, i) * 0.5
        sz_i = nk.operator.spin.sigmaz(hilbert, i) * 0.5
        
        sx_j = nk.operator.spin.sigmax(hilbert, j) * 0.5
        sy_j = nk.operator.spin.sigmay(hilbert, j) * 0.5
        sz_j = nk.operator.spin.sigmaz(hilbert, j) * 0.5
        
        spin_corr = (sx_i @ sx_j + sy_i @ sy_j + sz_i @ sz_j).to_jax_operator()
        operators.append(spin_corr)
    
    return operators

def test_parallel_performance(vqs, operators):
    """测试并行性能"""
    print(f"\n=== 测试 {len(operators)} 个算符 ===")
    
    # 顺序计算
    print("顺序计算...")
    start_time = time.time()
    sequential_results = []
    for i, op in enumerate(operators):
        result = vqs.expect(op)
        sequential_results.append(result)
        print(f"  完成算符 {i+1}/{len(operators)}")
    sequential_time = time.time() - start_time
    
    # 并行计算
    print("\n并行计算...")
    start_time = time.time()
    parallel_results = simple_parallel_expectation(vqs, operators)
    parallel_time = time.time() - start_time
    
    # 比较结果
    print(f"\n=== 结果比较 ===")
    print(f"顺序计算时间: {sequential_time:.3f}s")
    print(f"并行计算时间: {parallel_time:.3f}s")
    print(f"加速比: {sequential_time/parallel_time:.2f}x")
    
    # 检查准确性
    max_diff = 0
    for i, (seq, par) in enumerate(zip(sequential_results, parallel_results)):
        diff = abs(seq.mean - par.mean)
        max_diff = max(max_diff, diff)
        
        if i < 3:
            print(f"算符 {i}: 顺序={seq.mean:.6f}, 并行={par.mean:.6f}, 差异={diff:.2e}")
    
    print(f"最大差异: {max_diff:.2e}")
    
    # 判断是否成功
    success = parallel_time < sequential_time and max_diff < 1e-10
    print(f"测试{'成功' if success else '失败'}")
    
    return success

def main():
    """主函数"""
    print("=== 简化并行采样测试 ===")
    print(f"JAX设备: {jax.devices()}")
    
    try:
        # 创建测试系统
        lattice, hilbert, vqs = create_test_system(L=3)
        print(f"系统大小: {lattice.n_nodes} 个位点")
        
        # 创建测试算符
        operators = create_test_operators(hilbert, n_ops=4)
        print(f"测试算符数量: {len(operators)}")
        
        # 运行测试
        success = test_parallel_performance(vqs, operators)
        
        if success:
            print("\n✓ 并行采样实现成功！")
        else:
            print("\n✗ 并行采样需要进一步优化")
            
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
