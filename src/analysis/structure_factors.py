import numpy as np
import netket as nk
import os
import jax
import jax.numpy as jnp
from functools import partial
from jax import vmap
from src.utils.logging import log_message

def create_data_structure(correlation_data, k_points_x, k_points_y, 
                                   structure_factor_complex, lattice, 
                                   calculation_type, reference_info=None):
    """
    创建优化的数据结构
    
    参数:
    - correlation_data: 原始相关函数数据列表
    - k_points_x, k_points_y: k点坐标
    - structure_factor_complex: 复数结构因子
    - lattice: 晶格对象
    - calculation_type: 计算类型 ('spin', 'dimer', 'diag_dimer', 'plaquette')
    - reference_info: 参考点信息
    
    返回:
    - 优化的数据结构字典
    """
    import time
    
    # 提取相关函数数据
    n_pairs = len(correlation_data)
    positions = np.zeros((n_pairs, 2))
    values = np.zeros(n_pairs, dtype=complex)
    errors = np.zeros(n_pairs, dtype=complex)
    variances = np.zeros(n_pairs, dtype=complex)
    pair_indices = np.zeros((n_pairs, 2), dtype=int)
    
    # 根据计算类型提取不同的索引信息
    if calculation_type == 'spin':
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['i'], data['j']]
    
    elif calculation_type in ['dimer', 'diag_dimer']:
        directions = np.array([data['direction'] for data in correlation_data])
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['dimer_i_idx'], data['dimer_j_idx']]
    
    elif calculation_type == 'plaquette':
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['plaq_i_idx'], data['plaq_j_idx']]
    
    # 创建k点网格
    kx_grid, ky_grid = np.meshgrid(k_points_x, k_points_y)
    k_points_grid = np.stack([kx_grid, ky_grid], axis=2)
    
    # 构建优化的数据结构
    optimized_data = {
        'metadata': {
            'lattice_info': {
                'Lx': lattice.extent[0],
                'Ly': lattice.extent[1], 
                'N_sites': lattice.n_nodes
            },
            'k_grid': {
                'kx': k_points_x,
                'ky': k_points_y,
                'shape': structure_factor_complex.shape
            },
            'calculation_type': calculation_type,
            'reference_info': reference_info or {},
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        'correlations': {
            'positions': positions,
            'values': values,
            'errors': errors,
            'variances': variances,
            'pair_indices': pair_indices
        },
        'structure_factor': {
            'values': structure_factor_complex,
            'k_points': k_points_grid,
            'normalization': 1.0 / lattice.n_nodes
        }
    }
    
    # 为二聚体类型添加方向信息
    if calculation_type in ['dimer', 'diag_dimer']:
        optimized_data['correlations']['directions'] = directions
    
    return optimized_data

def save_optimized_data(data, save_path):
    """
    保存优化的数据结构
    
    参数:
    - data: 优化的数据结构
    - save_path: 保存路径
    """
    np.save(save_path, data)
    
def load_optimized_data(load_path):
    """
    加载优化的数据结构
    
    参数:
    - load_path: 加载路径
    
    返回:
    - 优化的数据结构
    """
    return np.load(load_path, allow_pickle=True).item()

# NOTE: In the functions below that calculate structure factors,
# the 'correlation_data' dictionary stores the correlation values.
# However, the 'error', 'error_imag', 'variance', and 'variance_imag' fields
# within 'correlation_data', as well as the derived 'errors_of_full_corr'
# and 'errors_imag_of_full_corr' in the final saved output,
# pertain to the statistical uncertainty of the two-operator
# expectation value (e.g., <S_0·S_r>, <D_0·D_r>, or <O_0·O_r>).

def create_k_mesh(lattice):
    """
    创建k点网格，根据晶格尺寸设置点数，使用π/L*n的方式生成k点, 范围从-π到π

    参数:
    - lattice: 晶格对象，从中获取Lx和Ly

    返回:
    - k_points_x: x方向的k点
    - k_points_y: y方向的k点
    - kx, ky: 网格化的k点
    """

    # 从晶格获取尺寸
    Lx, Ly = lattice.extent

    # 生成k点，范围为[-π, π]，确保包含端点
    # 使用linspace生成均匀分布的点
    k_points_x = np.linspace(-np.pi, np.pi, 2 * Lx + 1, endpoint=True)
    k_points_y = np.linspace(-np.pi, np.pi, 2 * Ly + 1, endpoint=True)
    kx, ky = np.meshgrid(k_points_x, k_points_y)

    return k_points_x, k_points_y, kx, ky

########################################################
# 计算spin structure factor
########################################################
def calculate_spin_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算自旋结构因子，现在使用真正的并行采样优化
    将调用新的并行版本
    """
    return calculate_spin_structure_factor_parallel(vqs, lattice, L, save_dir, log_file)

def calculate_dimer_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算二聚体结构因子，现在使用真正的并行采样优化
    将调用新的并行版本
    """
    return calculate_dimer_structure_factor_parallel(vqs, lattice, L, save_dir, log_file)

########################################################
# 计算plaquette structure factor
########################################################
def construct_plaquette_permutation(hilbert, plaq_sites):
    """
    构建简盘循环置换操作符，使用自旋交换算符
    P = S_{1,2} S_{2,3} S_{3,4} 实现循环置换 (1,2,3,4) -> (4,1,2,3)
    P^-1 = S_{1,4} S_{4,3} S_{3,2} 实现逆循环置换 (1,2,3,4) -> (2,3,4,1)

    其中 S_{i,j} = (1/2 + 2S_i·S_j) 是交换算符
    """
    a, b, c, d = plaq_sites

    # 构建各个位点的自旋算符
    S_a = [nk.operator.spin.sigmax(hilbert, a) * 0.5,
           nk.operator.spin.sigmay(hilbert, a) * 0.5,
           nk.operator.spin.sigmaz(hilbert, a) * 0.5]

    S_b = [nk.operator.spin.sigmax(hilbert, b) * 0.5,
           nk.operator.spin.sigmay(hilbert, b) * 0.5,
           nk.operator.spin.sigmaz(hilbert, b) * 0.5]

    S_c = [nk.operator.spin.sigmax(hilbert, c) * 0.5,
           nk.operator.spin.sigmay(hilbert, c) * 0.5,
           nk.operator.spin.sigmaz(hilbert, c) * 0.5]

    S_d = [nk.operator.spin.sigmax(hilbert, d) * 0.5,
           nk.operator.spin.sigmay(hilbert, d) * 0.5,
           nk.operator.spin.sigmaz(hilbert, d) * 0.5]

    # 将所有操作符转换为JAX操作符
    S_a = [op.to_jax_operator() for op in S_a]
    S_b = [op.to_jax_operator() for op in S_b]
    S_c = [op.to_jax_operator() for op in S_c]
    S_d = [op.to_jax_operator() for op in S_d]

    # 构建交换算符 S_{i,j} = (1/2 + 2S_i·S_j)
    def exchange_op(S_i, S_j):
        # 计算 S_i·S_j = S^x_i·S^x_j + S^y_i·S^y_j + S^z_i·S^z_j
        SiSj = S_i[0] @ S_j[0] + S_i[1] @ S_j[1] + S_i[2] @ S_j[2]
        # 返回 1/2 + 2(S_i·S_j)
        constant_op = nk.operator.LocalOperator(hilbert, constant=0.5).to_jax_operator()
        return constant_op + 2.0 * SiSj

    # 构建正向循环置换 P = S_{1,2} S_{2,3} S_{3,4}
    S_ab = exchange_op(S_a, S_b)
    S_bc = exchange_op(S_b, S_c)
    S_cd = exchange_op(S_c, S_d)

    # 构建逆向循环置换 P^-1 = S_{1,4} S_{4,3} S_{3,2}
    S_ad = exchange_op(S_a, S_d)
    S_dc = exchange_op(S_d, S_c)
    S_cb = exchange_op(S_c, S_b)

    # 组合操作符：P = S_{a,b} S_{b,c} S_{c,d}
    P = S_ab @ S_bc @ S_cd

    # 组合操作符：P^-1 = S_{a,d} S_{d,c} S_{c,b}
    P_inv = S_ad @ S_dc @ S_cb

    return P, P_inv

########################################################
# 计算对角二聚体-二聚体结构因子
########################################################    
def calculate_diag_dimer_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算对角二聚体结构因子，现在使用真正的并行采样优化
    将调用新的并行版本
    """
    return calculate_diag_dimer_structure_factor_parallel(vqs, lattice, L, save_dir, log_file)

def calculate_diag_dimer_structure_factor_parallel(vqs, lattice, L, save_dir, log_file=None):
    """
    计算对角二聚体-二聚体结构因子，使用真正的并行采样优化版本
    一次生成样本，批量计算所有算符期望值
    """
    # Try to determine a specific log_file name from save_dir components
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                log_file = os.path.join(os.path.dirname(save_dir), f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
                break

    log_message(log_file, "开始计算对角二聚体结构因子（真正并行采样版本）...")

    # 获取总位点数
    N = lattice.n_nodes

    # 创建k点网格
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)
    n_kx = len(k_points_x)
    n_ky = len(k_points_y)

    # 获取晶格尺寸
    Lx, Ly = lattice.extent

    # 识别对角二聚体
    log_message(log_file, "识别所有对角二聚体...")
    diag_dimers_nwse = []  # 西北-东南方向的对角二聚体
    diag_dimers_swne = []  # 西南-东北方向的对角二聚体
    diag_dimer_positions_nwse = []
    diag_dimer_positions_swne = []

    # 遍历所有节点，寻找对角二聚体
    for i in range(N):
        pos_i = np.array(lattice.positions[i])
        
        for j in range(N):
            if i == j:
                continue
                
            pos_j = np.array(lattice.positions[j])
            
            # 计算原始位移
            displacement = pos_j - pos_i
            
            # 考虑周期性边界条件，计算最短位移
            displacement_pbc = displacement.copy()
            
            # 处理x方向周期性
            if displacement[0] > Lx:
                displacement_pbc[0] -= 2 * Lx
            elif displacement[0] < -Lx:
                displacement_pbc[0] += 2 * Lx
                
            # 处理y方向周期性  
            if displacement[1] > Ly:
                displacement_pbc[1] -= 2 * Ly
            elif displacement[1] < -Ly:
                displacement_pbc[1] += 2 * Ly
            
            # 西北-东南方向：j在i的右下方（相对位移约为[1, -1]）
            if (abs(displacement_pbc[0] - 1.0) < 0.1 and 
                abs(displacement_pbc[1] + 1.0) < 0.1):
                if (i, j) not in diag_dimers_nwse and (j, i) not in diag_dimers_nwse:
                    diag_dimers_nwse.append((i, j))
                    dimer_center = 0.5 * (pos_i + pos_j)
                    diag_dimer_positions_nwse.append(dimer_center)
            
            # 西南-东北方向：j在i的右上方（相对位移约为[1, 1]）
            elif (abs(displacement_pbc[0] - 1.0) < 0.1 and 
                  abs(displacement_pbc[1] - 1.0) < 0.1):
                if (i, j) not in diag_dimers_swne and (j, i) not in diag_dimers_swne:
                    diag_dimers_swne.append((i, j))
                    dimer_center = 0.5 * (pos_i + pos_j)
                    diag_dimer_positions_swne.append(dimer_center)

    dimers = diag_dimers_nwse + diag_dimers_swne
    dimer_positions = diag_dimer_positions_nwse + diag_dimer_positions_swne
    n_dimers_nwse = len(diag_dimers_nwse)
    n_dimers_swne = len(diag_dimers_swne)

    if len(dimers) == 0:
        log_message(log_file, "警告: 没有找到对角二聚体！")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    n_dimers = len(dimers)
    log_message(log_file, f"总共找到 {n_dimers_nwse} 个西北-东南方向对角二聚体和 {n_dimers_swne} 个西南-东北方向对角二聚体")

    log_message(log_file, "预计算自旋操作符...")
    spin_ops_components = []
    for i in range(lattice.n_nodes):
        sx_i = nk.operator.spin.sigmax(vqs.hilbert, i) * 0.5
        sy_i = nk.operator.spin.sigmay(vqs.hilbert, i) * 0.5
        sz_i = nk.operator.spin.sigmaz(vqs.hilbert, i) * 0.5
        sx_i_jax = sx_i.to_jax_operator()
        sy_i_jax = sy_i.to_jax_operator()
        sz_i_jax = sz_i.to_jax_operator()
        spin_ops_components.append((sx_i_jax, sy_i_jax, sz_i_jax))

    log_message(log_file, "预计算对角二聚体操作符 D_k = S_k1·S_k2 ...")
    dimer_ops_list = []
    for i, (i1, i2) in enumerate(dimers):
        sx_i1, sy_i1, sz_i1 = spin_ops_components[i1]
        sx_i2, sy_i2, sz_i2 = spin_ops_components[i2]
        S_i1_dot_S_i2 = sx_i1 @ sx_i2 + sy_i1 @ sy_i2 + sz_i1 @ sz_i2
        dimer_ops_list.append(S_i1_dot_S_i2)

    log_message(log_file, "计算位移向量...")
    dimer_positions_np = np.array(dimer_positions)
    r_vectors = np.zeros((n_dimers, n_dimers, 2))
    for i in range(n_dimers):
        r_vectors[i] = dimer_positions_np - dimer_positions_np[i]

    # 批量构建所有需要计算的操作符
    all_operators = []
    operator_info = []

    # 处理西北-东南方向的对角二聚体
    if n_dimers_nwse > 0:
        ref_dimer_idx_nwse = 0
        op_D_ref_nwse = dimer_ops_list[ref_dimer_idx_nwse]
        dimer_ref_tuple_nwse = dimers[ref_dimer_idx_nwse]
        log_message(log_file, f"准备西北-东南方向对角二聚体相关函数计算...")

        for j_local_idx in range(n_dimers_nwse):
            j_global_idx = j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_nwse @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'diag_nwse',
                'ref_idx': ref_dimer_idx_nwse,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_nwse,
                'j_tuple': dimers[j_global_idx]
            })

    # 处理西南-东北方向的对角二聚体
    if n_dimers_swne > 0:
        ref_dimer_idx_swne = n_dimers_nwse
        op_D_ref_swne = dimer_ops_list[ref_dimer_idx_swne]
        dimer_ref_tuple_swne = dimers[ref_dimer_idx_swne]
        log_message(log_file, f"准备西南-东北方向对角二聚体相关函数计算...")

        for j_local_idx in range(n_dimers_swne):
            j_global_idx = n_dimers_nwse + j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_swne @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'diag_swne',
                'ref_idx': ref_dimer_idx_swne,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_swne,
                'j_tuple': dimers[j_global_idx]
            })

    # 使用并行采样批量计算所有操作符
    log_message(log_file, "使用并行采样批量计算对角二聚体相关函数...")
    
    correlation_results = batch_expect_with_shared_samples(vqs, all_operators)

    # 处理结果
    diag_dimer_correlation_data = []
    for idx, (corr_obj, info) in enumerate(zip(correlation_results, operator_info)):
        r_ref_j = r_vectors[info['ref_idx'], info['j_idx']]
        corr_full = corr_obj.mean

        direction = 'diag_nwse' if info['type'] == 'diag_nwse' else 'diag_swne'

        diag_dimer_correlation_data.append({
            'dimer_i_idx': info['ref_idx'],
            'dimer_j_idx': info['j_idx'],
            'dimer_i_sites': info['ref_tuple'],
            'dimer_j_sites': info['j_tuple'],
            'r_x': r_ref_j[0],
            'r_y': r_ref_j[1],
            'corr_full_real': corr_full.real,
            'corr_full_imag': corr_full.imag,
            'error': corr_obj.error_of_mean.real,
            'error_imag': corr_obj.error_of_mean.imag,
            'variance': corr_obj.variance.real,
            'variance_imag': corr_obj.variance.imag,
            'direction': direction
        })

    if not diag_dimer_correlation_data:
        log_message(log_file, "没有足够的数据点进行傅里叶变换。")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    log_message(log_file, "计算傅里叶变换...")

    r_values = np.array([[data['r_x'], data['r_y']] for data in diag_dimer_correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] for data in diag_dimer_correlation_data])

    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    compute_sf_vmap = vmap(compute_sf_for_k)
    sf_values = compute_sf_vmap(k_grid_jax)
    sf_values_2d = sf_values.reshape(n_ky, n_kx)
    diag_dimer_sf_complex = np.array(sf_values_2d)

    diag_dimer_sf_complex /= N

    # 保存优化的数据结构
    reference_info = {}
    if n_dimers_nwse > 0:
        reference_info['nwse_reference'] = {'dimer_idx': 0, 'sites': dimers[0]}
    if n_dimers_swne > 0:
        reference_info['swne_reference'] = {'dimer_idx': n_dimers_nwse, 'sites': dimers[n_dimers_nwse]}

    optimized_data = create_data_structure(
        correlation_data=diag_dimer_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=diag_dimer_sf_complex,
        lattice=lattice,
        calculation_type='diag_dimer',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "diag_dimer_data.npy"))

    log_message(log_file, "对角二聚体结构因子计算完成（使用真正并行采样）")

    return (k_points_x, k_points_y), (diag_dimer_sf_complex.real, diag_dimer_sf_complex.imag)

########################################################
# 计算简盘结构因子
########################################################    
def calculate_plaquette_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算简盘结构因子，现在使用真正的并行采样优化
    将调用新的并行版本  
    """
    return calculate_plaquette_structure_factor_parallel(vqs, lattice, L, save_dir, log_file)

def calculate_plaquette_structure_factor_parallel(vqs, lattice, L, save_dir, log_file=None):
    """
    计算简盘结构因子，使用真正的并行采样优化版本
    一次生成样本，批量计算所有算符期望值
    """
    # Try to determine a specific log_file name from save_dir components
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                log_file = os.path.join(os.path.dirname(save_dir), f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
                break

    log_message(log_file, "开始计算简盘结构因子（真正并行采样版本）...")

    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)
    n_kx = len(k_points_x)
    n_ky = len(k_points_y)

    # 识别所有可能的4节点plaquette
    log_message(log_file, "识别所有可能的4节点简盘...")
    plaquettes = []
    plaquette_positions = []
    
    N = lattice.n_nodes
    positions = [np.array(lattice.positions[i]) for i in range(N)]
    
    # 获取晶格尺寸用于周期性边界条件
    Lx, Ly = lattice.extent
    
    # 每个格点作为简盘的左下角，寻找右边、上边、右上角的格点
    for i in range(N):
        pos_i = positions[i]
        
        # 寻找右邻居（x坐标增加约1，考虑周期性边界）
        right_neighbor = None
        for j in range(N):
            if j == i:
                continue
            pos_j = positions[j]
            
            # 计算位移，考虑周期性边界条件
            dx = pos_j[0] - pos_i[0]
            dy = pos_j[1] - pos_i[1]
            
            # 处理x方向周期性边界
            if dx > Lx:
                dx -= 2 * Lx
            elif dx < -Lx:
                dx += 2 * Lx
            
            # 处理y方向周期性边界
            if dy > Ly:
                dy -= 2 * Ly
            elif dy < -Ly:
                dy += 2 * Ly
            
            if abs(dx - 1.0) < 0.1 and abs(dy) < 0.1:
                right_neighbor = j
                break
        
        # 寻找上邻居（y坐标增加约1，考虑周期性边界）
        up_neighbor = None
        for j in range(N):
            if j == i:
                continue
            pos_j = positions[j]
            
            # 计算位移，考虑周期性边界条件
            dx = pos_j[0] - pos_i[0]
            dy = pos_j[1] - pos_i[1]
            
            # 处理x方向周期性边界
            if dx > Lx:
                dx -= 2 * Lx
            elif dx < -Lx:
                dx += 2 * Lx
            
            # 处理y方向周期性边界
            if dy > Ly:
                dy -= 2 * Ly
            elif dy < -Ly:
                dy += 2 * Ly
            
            if abs(dx) < 0.1 and abs(dy - 1.0) < 0.1:
                up_neighbor = j
                break
        
        # 寻找右上邻居（x和y坐标都增加约1，考虑周期性边界）
        right_up_neighbor = None
        for j in range(N):
            if j == i:
                continue
            pos_j = positions[j]
            
            # 计算位移，考虑周期性边界条件
            dx = pos_j[0] - pos_i[0]
            dy = pos_j[1] - pos_i[1]
            
            # 处理x方向周期性边界
            if dx > Lx:
                dx -= 2 * Lx
            elif dx < -Lx:
                dx += 2 * Lx
            
            # 处理y方向周期性边界
            if dy > Ly:
                dy -= 2 * Ly
            elif dy < -Ly:
                dy += 2 * Ly
            
            if abs(dx - 1.0) < 0.1 and abs(dy - 1.0) < 0.1:
                right_up_neighbor = j
                break
        
        # 如果找到了所有三个邻居，就组成一个plaquette
        if (right_neighbor is not None and 
            up_neighbor is not None and 
            right_up_neighbor is not None):
            
            plaq = [i, right_neighbor, right_up_neighbor, up_neighbor]  # 逆时针顺序
            plaquettes.append(plaq)
            
            # 计算plaquette中心位置
            plaq_positions = [positions[node] for node in plaq]
            center_x = np.mean([pos[0] for pos in plaq_positions])
            center_y = np.mean([pos[1] for pos in plaq_positions])
            plaquette_positions.append(np.array([center_x, center_y]))

    log_message(log_file, f"找到 {len(plaquettes)} 个有效的4节点简盘")
    
    if len(plaquettes) == 0:
        log_message(log_file, "警告: 没有找到有效的简盘！")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    log_message(log_file, "预计算简盘操作符 P_k 和 P_k^-1...")
    plaquette_P_ops = []
    plaquette_Pinv_ops = []
    
    for i, plaq_sites in enumerate(plaquettes):
        P, P_inv = construct_plaquette_permutation(vqs.hilbert, plaq_sites)
        plaquette_P_ops.append(P)
        plaquette_Pinv_ops.append(P_inv)

    log_message(log_file, "计算位移向量...")
    plaquette_positions_np = np.array(plaquette_positions)
    n_plaq = len(plaquettes)
    r_vectors = np.zeros((n_plaq, n_plaq, 2))
    for i in range(n_plaq):
        r_vectors[i] = plaquette_positions_np - plaquette_positions_np[i]

    reference_plaq_idx = 0
    P_ref = plaquette_P_ops[reference_plaq_idx]
    Pinv_ref = plaquette_Pinv_ops[reference_plaq_idx]
    O_0_ref = P_ref + Pinv_ref

    # 批量构建所有需要计算的操作符
    all_operators = []
    log_message(log_file, "准备简盘相关函数计算...")
    for j_idx in range(n_plaq):
        P_j = plaquette_P_ops[j_idx]
        Pinv_j = plaquette_Pinv_ops[j_idx]
        O_r_j = P_j + Pinv_j
        operator_to_expect = O_r_j @ O_0_ref
        all_operators.append(operator_to_expect)

    # 使用并行采样批量计算所有操作符
    log_message(log_file, "使用并行采样批量计算简盘相关函数...")
    
    correlation_results = batch_expect_with_shared_samples(vqs, all_operators)

    # 处理结果
    plaquette_correlation_data = []
    for j_idx, corr_obj in enumerate(correlation_results):
        r_ref_j = r_vectors[reference_plaq_idx, j_idx]
        
        corr_full_raw_unconnected = corr_obj.mean
        
        # C(r) = (1/4) * <[P_r + P_r^-1][P_0 + P_0^-1]> 
        corr_full_scaled = 0.25 * corr_full_raw_unconnected
        
        # 缩放误差和方差
        error_scaled_real = 0.25 * corr_obj.error_of_mean.real
        error_scaled_imag = 0.25 * corr_obj.error_of_mean.imag
        variance_scaled_real = (0.25**2) * corr_obj.variance.real
        variance_scaled_imag = (0.25**2) * corr_obj.variance.imag

        plaquette_correlation_data.append({
            'plaq_i_idx': reference_plaq_idx, 'plaq_j_idx': j_idx,
            'r_x': r_ref_j[0], 'r_y': r_ref_j[1],
            'corr_full_real': corr_full_scaled.real,
            'corr_full_imag': corr_full_scaled.imag,
            'error': error_scaled_real, 
            'error_imag': error_scaled_imag,
            'variance': variance_scaled_real,
            'variance_imag': variance_scaled_imag
        })

    log_message(log_file, "计算傅里叶变换...")
    r_values = np.array([[data['r_x'], data['r_y']] for data in plaquette_correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] for data in plaquette_correlation_data])

    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    compute_sf_vmap = vmap(compute_sf_for_k)
    sf_values = compute_sf_vmap(k_grid_jax)
    sf_values_2d = sf_values.reshape(n_ky, n_kx)
    plaq_sf_complex = np.array(sf_values_2d)

    N_sites = lattice.n_nodes
    plaq_sf_complex /= N_sites

    # 保存优化的数据结构
    reference_info = {
        'reference_plaquette': {
            'plaq_idx': reference_plaq_idx,
            'sites': plaquettes[reference_plaq_idx]
        },
        'total_plaquettes': len(plaquettes)
    }
    
    optimized_data = create_data_structure(
        correlation_data=plaquette_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=plaq_sf_complex,
        lattice=lattice,
        calculation_type='plaquette',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "plaquette_data.npy"))

    log_message(log_file, "简盘结构因子计算完成（使用真正并行采样）")

    return (k_points_x, k_points_y), (plaq_sf_complex.real, plaq_sf_complex.imag)


def analyze_optimized_structure_factor(data_path, analysis_type='basic'):
    """
    分析优化数据结构中的结构因子
    
    参数:
    - data_path: 优化数据文件路径
    - analysis_type: 分析类型 ('basic', 'peaks', 'correlations')
    
    返回:
    - 分析结果字典
    """
    # 加载数据
    data = load_optimized_data(data_path)
    
    # 基本信息
    metadata = data['metadata']
    sf_values = data['structure_factor']['values']
    k_points = data['structure_factor']['k_points']
    correlations = data['correlations']['values']
    
    results = {
        'metadata': metadata,
        'structure_factor_shape': sf_values.shape,
        'max_sf_value': np.max(np.abs(sf_values)),
        'min_sf_value': np.min(np.abs(sf_values))
    }
    
    if analysis_type in ['basic', 'peaks']:
        # 找到峰值位置
        sf_magnitude = np.abs(sf_values)
        peak_idx = np.unravel_index(np.argmax(sf_magnitude), sf_magnitude.shape)
        peak_k = k_points[peak_idx]
        
        results['peak_analysis'] = {
            'peak_position_idx': peak_idx,
            'peak_k_vector': peak_k,
            'peak_value': sf_values[peak_idx],
            'peak_magnitude': sf_magnitude[peak_idx]
        }
    
    if analysis_type in ['basic', 'correlations']:
        # 相关函数统计
        positions = data['correlations']['positions']
        correlation_magnitudes = np.abs(correlations)
        
        results['correlation_analysis'] = {
            'total_correlations': len(correlations),
            'max_correlation': np.max(correlation_magnitudes),
            'min_correlation': np.min(correlation_magnitudes),
            'mean_correlation': np.mean(correlation_magnitudes),
            'std_correlation': np.std(correlation_magnitudes),
            'max_distance': np.max(np.linalg.norm(positions, axis=1))
        }
    
    return results



def plot_structure_factor_from_optimized(data_path, save_path=None):
    """
    从优化数据结构绘制结构因子
    
    参数:
    - data_path: 优化数据文件路径
    - save_path: 图片保存路径（可选）
    
    返回:
    - matplotlib figure对象
    """
    try:
        import matplotlib.pyplot as plt
        
        # 加载数据
        data = load_optimized_data(data_path)
        sf_values = data['structure_factor']['values']
        k_points = data['structure_factor']['k_points']
        metadata = data['metadata']
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 绘制结构因子实部
        im1 = ax1.imshow(sf_values.real, origin='lower', aspect='auto')
        ax1.set_title(f'{metadata["calculation_type"]} Structure Factor (Real)')
        ax1.set_xlabel('kx index')
        ax1.set_ylabel('ky index')
        plt.colorbar(im1, ax=ax1)
        
        # 绘制结构因子幅值
        im2 = ax2.imshow(np.abs(sf_values), origin='lower', aspect='auto')
        ax2.set_title(f'{metadata["calculation_type"]} Structure Factor (Magnitude)')
        ax2.set_xlabel('kx index')
        ax2.set_ylabel('ky index')
        plt.colorbar(im2, ax=ax2)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
        
    except ImportError:
        print("Warning: matplotlib not available, cannot plot structure factor")
        return None

# 优化的操作符缓存和批量计算模块

class OperatorCache:
    """
    操作符缓存类，避免重复创建相同的操作符
    """
    def __init__(self, hilbert):
        self.hilbert = hilbert
        self._spin_ops = {}  # 缓存自旋操作符
        self._dimer_ops = {}  # 缓存二聚体操作符
        self._exchange_ops = {}  # 缓存交换算符
        
    def get_spin_operators(self, site_idx):
        """获取指定位点的自旋操作符（缓存版本）"""
        if site_idx not in self._spin_ops:
            sx = nk.operator.spin.sigmax(self.hilbert, site_idx) * 0.5
            sy = nk.operator.spin.sigmay(self.hilbert, site_idx) * 0.5
            sz = nk.operator.spin.sigmaz(self.hilbert, site_idx) * 0.5
            # 转换为JAX操作符
            self._spin_ops[site_idx] = (
                sx.to_jax_operator(),
                sy.to_jax_operator(), 
                sz.to_jax_operator()
            )
        return self._spin_ops[site_idx]
    
    def get_dimer_operator(self, site1, site2):
        """获取二聚体操作符 S_i·S_j（缓存版本）"""
        key = tuple(sorted([site1, site2]))
        if key not in self._dimer_ops:
            sx1, sy1, sz1 = self.get_spin_operators(site1)
            sx2, sy2, sz2 = self.get_spin_operators(site2)
            dimer_op = sx1 @ sx2 + sy1 @ sy2 + sz1 @ sz2
            self._dimer_ops[key] = dimer_op
        return self._dimer_ops[key]
    
    def get_exchange_operator(self, site1, site2):
        """获取交换算符 S_{i,j} = 1/2 + 2S_i·S_j（缓存版本）"""
        key = tuple(sorted([site1, site2]))
        if key not in self._exchange_ops:
            dimer_op = self.get_dimer_operator(site1, site2)
            constant_op = nk.operator.LocalOperator(self.hilbert, constant=0.5).to_jax_operator()
            exchange_op = constant_op + 2.0 * dimer_op
            self._exchange_ops[key] = exchange_op
        return self._exchange_ops[key]

def batch_expectation_values(vqs, operators, batch_size=None):
    """
    改进的批量计算操作符期望值函数
    使用共享样本集来计算所有算符，避免重复采样
    
    参数:
    - vqs: 变分量子态
    - operators: 操作符列表
    - batch_size: 批次大小，用于内存管理
    
    返回:
    - 期望值列表
    """
    if batch_size is None or len(operators) <= batch_size:
        # 如果操作符数量不多，一次性计算所有
        return batch_expect_with_shared_samples(vqs, operators)
    
    # 如果操作符太多，分批处理但每批内共享样本
    results = []
    for i in range(0, len(operators), batch_size):
        batch_ops = operators[i:i+batch_size]
        batch_results = batch_expect_with_shared_samples(vqs, batch_ops)
        results.extend(batch_results)
    
    return results

def optimized_plaquette_permutation(hilbert, plaq_sites, op_cache):
    """
    优化的plaquette循环置换操作符构建
    使用操作符缓存避免重复计算
    """
    a, b, c, d = plaq_sites
    
    # 使用缓存的交换算符
    S_ab = op_cache.get_exchange_operator(a, b)
    S_bc = op_cache.get_exchange_operator(b, c) 
    S_cd = op_cache.get_exchange_operator(c, d)
    S_ad = op_cache.get_exchange_operator(a, d)
    S_dc = op_cache.get_exchange_operator(d, c)
    S_cb = op_cache.get_exchange_operator(c, b)
    
    # 构建循环置换
    P = S_ab @ S_bc @ S_cd
    P_inv = S_ad @ S_dc @ S_cb
    
    return P, P_inv

# 真正的GPU并行采样函数
def batch_expect_with_shared_samples(vqs, operators, n_samples=None, use_advanced_parallel=True):
    """
    使用真正的GPU并行采样来批量计算所有算符的期望值
    一次生成样本，同时计算所有算符的期望值，实现真正的并行化

    参数:
    - vqs: 变分量子态
    - operators: 算符列表
    - n_samples: 样本数量，如果为None则使用vqs的默认值
    - use_advanced_parallel: 是否使用高级并行实现

    返回:
    - 期望值结果列表
    """
    if len(operators) == 0:
        return []

    if len(operators) == 1:
        # 单个算符直接计算
        return [vqs.expect(operators[0])]

    # 根据算符数量选择并行策略
    if use_advanced_parallel and len(operators) > 4:
        log_message(None, f"使用高级GPU并行采样处理 {len(operators)} 个算符...")
        return advanced_parallel_expectation(vqs, operators, n_samples)
    else:
        log_message(None, f"使用标准GPU并行采样处理 {len(operators)} 个算符...")
        return standard_parallel_expectation(vqs, operators, n_samples)


def standard_parallel_expectation(vqs, operators, n_samples=None):
    """
    标准的GPU并行采样实现
    """
    if n_samples is not None:
        original_n_samples = vqs.n_samples
        vqs.n_samples = n_samples

    try:
        # 生成一次样本，用于所有算符的计算
        log_message(None, f"生成 {vqs.n_samples} 个样本用于 {len(operators)} 个算符的并行计算...")

        # 获取样本和对数概率
        samples = vqs.samples
        log_psi = vqs.log_value(samples)

        # 使用向量化的方式并行计算所有算符
        results = vectorized_batch_expectation(vqs, samples, log_psi, operators)

        log_message(None, f"完成所有 {len(operators)} 个算符的并行计算")
        return results

    finally:
        if n_samples is not None:
            vqs.n_samples = original_n_samples


@jax.jit
def compute_local_energy_jit(samples, log_psi_samples, conn_configs, mels, log_psi_conn):
    """
    JIT编译的局域能量计算函数

    参数:
    - samples: 原始样本配置
    - log_psi_samples: 原始样本的对数概率
    - conn_configs: 连接的配置
    - mels: 矩阵元素
    - log_psi_conn: 连接配置的对数概率

    返回:
    - 局域能量数组
    """
    n_samples = samples.shape[0]
    n_conn_per_sample = conn_configs.shape[0] // n_samples

    # 计算对数概率比
    log_psi_expanded = jnp.repeat(log_psi_samples, n_conn_per_sample)
    log_prob_ratios = log_psi_conn - log_psi_expanded

    # 计算加权矩阵元素
    weighted_mels = mels * jnp.exp(log_prob_ratios)

    # 重塑并求和得到局域能量
    local_energies_reshaped = weighted_mels.reshape(n_samples, -1)
    local_energies = jnp.sum(local_energies_reshaped, axis=1)

    return local_energies


def vectorized_batch_expectation(vqs, samples, log_psi, operators):
    """
    向量化批量计算算符期望值
    使用JIT编译的函数加速计算

    参数:
    - vqs: 变分量子态
    - samples: 样本配置
    - log_psi: 样本的对数概率
    - operators: 算符列表

    返回:
    - 期望值结果列表
    """
    results = []

    # 分批处理以避免内存问题
    batch_size = min(8, len(operators))  # 进一步减小批次大小

    for i in range(0, len(operators), batch_size):
        batch_ops = operators[i:i+batch_size]
        batch_results = []

        # 对当前批次的算符计算期望值
        for operator in batch_ops:
            try:
                # 使用NetKet的内置方法计算局域能量
                # 这是更兼容的方法
                local_energies = operator.get_local_kernel(samples, vqs.log_value)

                # 计算统计量
                mean_val = jnp.mean(local_energies)
                variance_val = jnp.var(local_energies)
                error_val = jnp.sqrt(variance_val / samples.shape[0])

                # 创建统计对象
                stats = nk.stats.Stats(
                    mean=mean_val,
                    error_of_mean=error_val,
                    variance=variance_val
                )

                batch_results.append(stats)

            except Exception as e:
                log_message(None, f"算符计算出错，回退到标准方法: {e}")
                # 回退到标准方法
                stats = vqs.expect(operator)
                batch_results.append(stats)

        results.extend(batch_results)

        if i + batch_size < len(operators):
            log_message(None, f"完成 {i + batch_size}/{len(operators)} 个算符的计算...")

    return results

# 高级GPU并行采样实现
def advanced_parallel_expectation(vqs, operators, n_samples=None):
    """
    使用JAX的高级并行功能实现真正的GPU并行采样
    这个版本尝试最大化GPU利用率

    参数:
    - vqs: 变分量子态
    - operators: 算符列表
    - n_samples: 样本数量

    返回:
    - 期望值结果列表
    """
    if len(operators) == 0:
        return []

    if len(operators) == 1:
        return [vqs.expect(operators[0])]

    if n_samples is not None:
        original_n_samples = vqs.n_samples
        vqs.n_samples = n_samples

    try:
        log_message(None, f"使用高级GPU并行采样计算 {len(operators)} 个算符...")

        # 生成共享样本
        samples = vqs.samples

        # 预处理所有算符的局域能量
        operator_data = []
        for i, operator in enumerate(operators):
            try:
                # 使用NetKet的标准方法计算局域能量
                local_energies = operator.get_local_kernel(samples, vqs.log_value)
                operator_data.append(local_energies)
            except Exception as e:
                log_message(None, f"算符 {i} 预处理失败: {e}")
                operator_data.append(None)

        # 并行处理所有算符
        all_results = []

        for i, (operator, local_energies) in enumerate(zip(operators, operator_data)):
            if local_energies is None:
                # 预处理失败的算符回退到标准方法
                log_message(None, f"回退处理算符 {i}...")
                result = vqs.expect(operator)
                all_results.append(result)
            else:
                # 使用预计算的局域能量
                # 计算统计量
                mean_val = jnp.mean(local_energies)
                variance_val = jnp.var(local_energies)
                error_val = jnp.sqrt(variance_val / samples.shape[0])

                # 创建统计对象
                stats = nk.stats.Stats(
                    mean=mean_val,
                    error_of_mean=error_val,
                    variance=variance_val
                )

                all_results.append(stats)

        log_message(None, f"完成所有 {len(operators)} 个算符的高级并行计算")
        return all_results

    finally:
        if n_samples is not None:
            vqs.n_samples = original_n_samples


# 简化的并行处理辅助函数
@jax.jit
def compute_batch_statistics(local_energies_list):
    """
    批量计算多个局域能量数组的统计量

    参数:
    - local_energies_list: 局域能量数组列表

    返回:
    - 统计量列表
    """
    results = []

    for local_energies in local_energies_list:
        n_samples = local_energies.shape[0]
        mean_val = jnp.mean(local_energies)
        variance_val = jnp.var(local_energies)
        error_val = jnp.sqrt(variance_val / n_samples)

        results.append((mean_val, error_val, variance_val))

    return results

# 改进的结构因子计算函数，使用真正的并行采样
def calculate_spin_structure_factor_parallel(vqs, lattice, L, save_dir, log_file=None):
    """
    计算自旋结构因子，使用真正的并行采样优化版本
    一次生成样本，批量计算所有算符期望值
    """
    # Try to determine a specific log_file name from save_dir components
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                log_file = os.path.join(os.path.dirname(save_dir), f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
                break

    log_message(log_file, "开始计算自旋结构因子（真正并行采样版本）...")

    N = lattice.n_nodes

    # 创建k点网格
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)
    n_kx = len(k_points_x)
    n_ky = len(k_points_y)

    # 初始化操作符缓存
    log_message(log_file, "初始化操作符缓存...")
    op_cache = OperatorCache(vqs.hilbert)

    # 创建位置矩阵，用于向量化计算
    positions = np.array([lattice.positions[i] for i in range(N)])

    # 计算所有位点对之间的位移向量
    log_message(log_file, "计算位移向量...")
    r_vectors = np.zeros((N, N, 2))
    for i in range(N):
        r_vectors[i] = positions - positions[i]

    # 预构建所有自旋点积操作符 S_0·S_j
    log_message(log_file, "预构建所有自旋相关操作符...")
    ops_S0_dot_Sj_list = []
    for j in range(N):
        spin_dot_op = op_cache.get_dimer_operator(0, j)
        ops_S0_dot_Sj_list.append(spin_dot_op)

    # 使用改进的批量计算（真正的并行采样）
    log_message(log_file, "使用并行采样批量计算自旋相关函数...")
    
    correlation_results = batch_expect_with_shared_samples(vqs, ops_S0_dot_Sj_list)

    # 处理结果
    correlation_data = []
    update_interval = max(1, N // 10)
    
    for j, corr_obj in enumerate(correlation_results):
        r_0j = r_vectors[0, j]
        unconnected_corr_S0_Sj_mean = corr_obj.mean

        corr_full_real = unconnected_corr_S0_Sj_mean.real
        corr_full_imag = unconnected_corr_S0_Sj_mean.imag
        
        current_error_real: float
        current_error_imag: float
        current_variance_real: float
        current_variance_imag: float

        if j == 0: 
            current_error_real = 0.0
            current_error_imag = 0.0
            current_variance_real = 0.0
            current_variance_imag = 0.0
        else:
            current_error_real = corr_obj.error_of_mean.real
            current_error_imag = corr_obj.error_of_mean.imag
            current_variance_real = corr_obj.variance.real
            current_variance_imag = corr_obj.variance.imag

        correlation_data.append({
            'i': 0, 'j': j,
            'r_x': r_0j[0], 'r_y': r_0j[1],
            'corr_full_real': corr_full_real,
            'corr_full_imag': corr_full_imag,
            'error': current_error_real, 
            'error_imag': current_error_imag, 
            'variance': current_variance_real, 
            'variance_imag': current_variance_imag
        })
        if j % update_interval == 0 or j == N - 1:
            log_message(log_file, f"处理相关函数进度: {j+1}/{N}")

    # 向量化计算傅里叶变换
    log_message(log_file, "计算傅里叶变换...")

    # 提取相关数据用于向量化计算
    r_values = np.array([[data['r_x'], data['r_y']] for data in correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] for data in correlation_data])

    # 使用已经创建的k网格
    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)

    # 将数据转换为JAX数组
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    # 定义计算单个k点的结构因子的函数
    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    # 向量化函数以并行计算所有k点
    compute_sf_vmap = vmap(compute_sf_for_k)

    # 并行计算所有k点的结构因子
    sf_values = compute_sf_vmap(k_grid_jax)

    # 将结果重塑为2D网格
    sf_values_2d = sf_values.reshape(n_ky, n_kx)

    # 存储结果
    spin_sf = np.array(sf_values_2d)

    # 归一化
    spin_sf /= N

    # 保存优化的数据结构
    optimized_data = create_data_structure(
        correlation_data=correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=spin_sf,
        lattice=lattice,
        calculation_type='spin',
        reference_info={'reference_site': 0}
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "spin_data.npy"))

    log_message(log_file, "自旋结构因子计算完成（使用真正并行采样）")

    return (k_points_x, k_points_y), (spin_sf.real, spin_sf.imag)

def calculate_dimer_structure_factor_parallel(vqs, lattice, L, save_dir, log_file=None):
    """
    计算二聚体-二聚体结构因子，使用真正的并行采样优化版本
    一次生成样本，批量计算所有算符期望值
    """
    # Try to determine a specific log_file name from save_dir components
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                log_file = os.path.join(os.path.dirname(save_dir), f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
                break
    
    log_message(log_file, "开始计算二聚体结构因子（真正并行采样版本）...")

    # 获取总位点数
    N = lattice.n_nodes

    # 创建k点网格
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)
    n_kx = len(k_points_x)
    n_ky = len(k_points_y)

    # 收集所有方向的二聚体键（分别处理x和y方向）
    log_message(log_file, "识别x和y方向的二聚体...")
    dimers_x = []  # x方向的二聚体
    dimers_y = []  # y方向的二聚体
    dimer_positions_x = []
    dimer_positions_y = []

    # 预先获取所有边
    edges = list(lattice.edges(color=0))

    for edge in edges:
        site_i, site_j = edge
        if site_i > site_j:
            site_i, site_j = site_j, site_i

        pos_i = np.array(lattice.positions[site_i])
        pos_j = np.array(lattice.positions[site_j])
        displacement = pos_j - pos_i

        # 检查是否为单位长度的x方向dimer
        if abs(abs(displacement[0]) - 1.0) < 0.1 and abs(displacement[1]) < 0.1:
            dimers_x.append((site_i, site_j))
            dimer_center = 0.5 * (pos_i + pos_j)
            dimer_positions_x.append(dimer_center)
        # 检查是否为单位长度的y方向dimer
        elif abs(abs(displacement[1]) - 1.0) < 0.1 and abs(displacement[0]) < 0.1:
            dimers_y.append((site_i, site_j))
            dimer_center = 0.5 * (pos_i + pos_j)
            dimer_positions_y.append(dimer_center)

    dimers = dimers_x + dimers_y
    dimer_positions = dimer_positions_x + dimer_positions_y
    n_dimers_x = len(dimers_x)
    n_dimers_y = len(dimers_y)

    if len(dimers) == 0:
        log_message(log_file, "警告: 没有找到二聚体！")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    n_dimers = len(dimers)

    log_message(log_file, "预计算自旋操作符...")
    spin_ops_components = []
    for i in range(lattice.n_nodes):
        sx_i = nk.operator.spin.sigmax(vqs.hilbert, i) * 0.5
        sy_i = nk.operator.spin.sigmay(vqs.hilbert, i) * 0.5
        sz_i = nk.operator.spin.sigmaz(vqs.hilbert, i) * 0.5
        sx_i_jax = sx_i.to_jax_operator()
        sy_i_jax = sy_i.to_jax_operator()
        sz_i_jax = sz_i.to_jax_operator()
        spin_ops_components.append((sx_i_jax, sy_i_jax, sz_i_jax))

    log_message(log_file, "预计算二聚体操作符 D_k = S_k1·S_k2 ...")
    dimer_ops_list = []
    for i, (i1, i2) in enumerate(dimers):
        sx_i1, sy_i1, sz_i1 = spin_ops_components[i1]
        sx_i2, sy_i2, sz_i2 = spin_ops_components[i2]
        S_i1_dot_S_i2 = sx_i1 @ sx_i2 + sy_i1 @ sy_i2 + sz_i1 @ sz_i2
        dimer_ops_list.append(S_i1_dot_S_i2)

    log_message(log_file, "计算位移向量...")
    dimer_positions_np = np.array(dimer_positions)
    r_vectors = np.zeros((n_dimers, n_dimers, 2))
    for i in range(n_dimers):
        r_vectors[i] = dimer_positions_np - dimer_positions_np[i]

    dimer_correlation_data = []

    # 批量构建所有需要计算的操作符
    all_operators = []
    operator_info = []  # 存储操作符的元信息
    
    if n_dimers_x > 0:
        ref_dimer_idx_x = 0
        op_D_ref_x = dimer_ops_list[ref_dimer_idx_x]
        dimer_ref_tuple_x = dimers[ref_dimer_idx_x]
        log_message(log_file, f"准备x方向二聚体相关函数计算...")
        
        for j_local_idx in range(n_dimers_x):
            j_global_idx = j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_x @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'x_dimer',
                'ref_idx': ref_dimer_idx_x,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_x,
                'j_tuple': dimers[j_global_idx]
            })

    if n_dimers_y > 0:
        ref_dimer_idx_y = n_dimers_x
        op_D_ref_y = dimer_ops_list[ref_dimer_idx_y]
        dimer_ref_tuple_y = dimers[ref_dimer_idx_y]
        log_message(log_file, f"准备y方向二聚体相关函数计算...")

        for j_local_idx in range(n_dimers_y):
            j_global_idx = n_dimers_x + j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_y @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'y_dimer',
                'ref_idx': ref_dimer_idx_y,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_y,
                'j_tuple': dimers[j_global_idx]
            })

    # 使用并行采样批量计算所有操作符
    log_message(log_file, "使用并行采样批量计算二聚体相关函数...")
    
    correlation_results = batch_expect_with_shared_samples(vqs, all_operators)

    # 处理结果
    for corr_obj, info in zip(correlation_results, operator_info):
        r_ref_j = r_vectors[info['ref_idx'], info['j_idx']]
        corr_full = corr_obj.mean

        direction = 'x' if info['type'] == 'x_dimer' else 'y'

        dimer_correlation_data.append({
            'dimer_i_idx': info['ref_idx'],
            'dimer_j_idx': info['j_idx'],
            'dimer_i_sites': info['ref_tuple'],
            'dimer_j_sites': info['j_tuple'],
            'r_x': r_ref_j[0],
            'r_y': r_ref_j[1],
            'corr_full_real': corr_full.real,
            'corr_full_imag': corr_full.imag,
            'error': corr_obj.error_of_mean.real,
            'error_imag': corr_obj.error_of_mean.imag,
            'variance': corr_obj.variance.real,
            'variance_imag': corr_obj.variance.imag,
            'direction': direction
        })

    if not dimer_correlation_data:
        log_message(log_file, "没有足够的数据点进行傅里叶变换。")
        return (k_points_x, k_points_y), (np.zeros((n_ky, n_kx)), np.zeros((n_ky, n_kx)))

    log_message(log_file, "计算傅里叶变换...")

    r_values = np.array([[data['r_x'], data['r_y']] for data in dimer_correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] for data in dimer_correlation_data])

    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    compute_sf_vmap = vmap(compute_sf_for_k)
    sf_values = compute_sf_vmap(k_grid_jax)
    sf_values_2d = sf_values.reshape(n_ky, n_kx)
    dimer_sf_complex = np.array(sf_values_2d)

    dimer_sf_complex /= N

    # 保存优化的数据结构
    reference_info = {}
    if n_dimers_x > 0:
        reference_info['x_reference'] = {'dimer_idx': 0, 'sites': dimers[0]}
    if n_dimers_y > 0:
        reference_info['y_reference'] = {'dimer_idx': n_dimers_x, 'sites': dimers[n_dimers_x]}

    optimized_data = create_data_structure(
        correlation_data=dimer_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=dimer_sf_complex,
        lattice=lattice,
        calculation_type='dimer',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "dimer_data.npy"))

    log_message(log_file, "二聚体结构因子计算完成（使用真正并行采样）")

    return (k_points_x, k_points_y), (dimer_sf_complex.real, dimer_sf_complex.imag)




