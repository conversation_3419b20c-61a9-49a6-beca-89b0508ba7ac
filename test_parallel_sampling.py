#!/usr/bin/env python3
"""
测试脚本：验证新的GPU并行采样实现
"""

import os
import sys
import time

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置环境变量
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"
os.environ["JAX_PLATFORM_NAME"] = "gpu"

import jax
import jax.numpy as jnp
import netket as nk
import numpy as np

# 简化的日志函数
def log_message(log_file, message):
    print(message)

# 导入并行采样函数的核心部分
import importlib.util
spec = importlib.util.spec_from_file_location("structure_factors", "src/analysis/structure_factors.py")
sf_module = importlib.util.module_from_spec(spec)

# 手动设置log_message函数
sf_module.log_message = log_message

try:
    spec.loader.exec_module(sf_module)
    batch_expect_with_shared_samples = sf_module.batch_expect_with_shared_samples
    advanced_parallel_expectation = sf_module.advanced_parallel_expectation
    standard_parallel_expectation = sf_module.standard_parallel_expectation
except Exception as e:
    print(f"导入错误: {e}")
    # 创建简化的测试版本
    def batch_expect_with_shared_samples(vqs, operators, n_samples=None, use_advanced_parallel=True):
        return [vqs.expect(op) for op in operators]

    def advanced_parallel_expectation(vqs, operators, n_samples=None):
        return [vqs.expect(op) for op in operators]

    def standard_parallel_expectation(vqs, operators, n_samples=None):
        return [vqs.expect(op) for op in operators]

def create_test_system(L=4):
    """
    创建一个简单的测试系统
    """
    # 创建正方形晶格
    lattice = nk.graph.Square(L, pbc=True)
    
    # 创建Hilbert空间
    hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes)
    
    # 创建简单的变分态
    model = nk.models.RBM(alpha=1, param_dtype=complex)
    
    # 创建采样器
    sampler = nk.sampler.MetropolisLocal(hilbert)
    
    # 创建变分量子态
    vqs = nk.vqs.MCState(
        sampler=sampler,
        model=model,
        n_samples=1024,
        n_discard_per_chain=0,
        chunk_size=256
    )
    
    return lattice, hilbert, vqs

def create_test_operators(hilbert, n_ops=10):
    """
    创建测试算符
    """
    operators = []
    N = hilbert.size
    
    # 创建自旋-自旋相关算符
    for i in range(min(n_ops, N)):
        j = (i + 1) % N
        
        # S_i · S_j 算符
        sx_i = nk.operator.spin.sigmax(hilbert, i) * 0.5
        sy_i = nk.operator.spin.sigmay(hilbert, i) * 0.5
        sz_i = nk.operator.spin.sigmaz(hilbert, i) * 0.5
        
        sx_j = nk.operator.spin.sigmax(hilbert, j) * 0.5
        sy_j = nk.operator.spin.sigmay(hilbert, j) * 0.5
        sz_j = nk.operator.spin.sigmaz(hilbert, j) * 0.5
        
        spin_corr = (sx_i @ sx_j + sy_i @ sy_j + sz_i @ sz_j).to_jax_operator()
        operators.append(spin_corr)
    
    return operators

def test_parallel_vs_sequential(vqs, operators):
    """
    测试并行采样与顺序采样的性能和准确性
    """
    print(f"测试 {len(operators)} 个算符的并行采样...")
    
    # 顺序计算（基准）
    print("顺序计算...")
    start_time = time.time()
    sequential_results = []
    for op in operators:
        result = vqs.expect(op)
        sequential_results.append(result)
    sequential_time = time.time() - start_time
    
    # 标准并行计算
    print("标准并行计算...")
    start_time = time.time()
    parallel_results = standard_parallel_expectation(vqs, operators)
    parallel_time = time.time() - start_time
    
    # 高级并行计算
    print("高级并行计算...")
    start_time = time.time()
    advanced_results = advanced_parallel_expectation(vqs, operators)
    advanced_time = time.time() - start_time
    
    # 比较结果
    print("\n=== 性能比较 ===")
    print(f"顺序计算时间: {sequential_time:.3f}s")
    print(f"标准并行时间: {parallel_time:.3f}s (加速比: {sequential_time/parallel_time:.2f}x)")
    print(f"高级并行时间: {advanced_time:.3f}s (加速比: {sequential_time/advanced_time:.2f}x)")
    
    # 比较准确性
    print("\n=== 准确性比较 ===")
    max_diff_standard = 0
    max_diff_advanced = 0
    
    for i, (seq, par, adv) in enumerate(zip(sequential_results, parallel_results, advanced_results)):
        diff_standard = abs(seq.mean - par.mean)
        diff_advanced = abs(seq.mean - adv.mean)
        
        max_diff_standard = max(max_diff_standard, diff_standard)
        max_diff_advanced = max(max_diff_advanced, diff_advanced)
        
        if i < 3:  # 只显示前3个结果
            print(f"算符 {i}:")
            print(f"  顺序: {seq.mean:.6f} ± {seq.error_of_mean:.6f}")
            print(f"  标准: {par.mean:.6f} ± {par.error_of_mean:.6f} (差异: {diff_standard:.2e})")
            print(f"  高级: {adv.mean:.6f} ± {adv.error_of_mean:.6f} (差异: {diff_advanced:.2e})")
    
    print(f"\n最大差异 - 标准并行: {max_diff_standard:.2e}")
    print(f"最大差异 - 高级并行: {max_diff_advanced:.2e}")
    
    return {
        'sequential_time': sequential_time,
        'parallel_time': parallel_time,
        'advanced_time': advanced_time,
        'max_diff_standard': max_diff_standard,
        'max_diff_advanced': max_diff_advanced
    }

def main():
    """
    主测试函数
    """
    print("=== GPU并行采样测试 ===")
    print(f"JAX设备: {jax.devices()}")
    
    # 创建测试系统
    lattice, hilbert, vqs = create_test_system(L=4)
    print(f"系统大小: {lattice.n_nodes} 个位点")
    
    # 创建测试算符
    operators = create_test_operators(hilbert, n_ops=8)
    print(f"测试算符数量: {len(operators)}")
    
    # 运行测试
    results = test_parallel_vs_sequential(vqs, operators)
    
    # 判断测试是否通过
    print("\n=== 测试结果 ===")
    
    # 性能测试
    if results['parallel_time'] < results['sequential_time']:
        print("✓ 标准并行采样性能提升")
    else:
        print("✗ 标准并行采样性能未提升")
    
    if results['advanced_time'] < results['sequential_time']:
        print("✓ 高级并行采样性能提升")
    else:
        print("✗ 高级并行采样性能未提升")
    
    # 准确性测试
    tolerance = 1e-10
    if results['max_diff_standard'] < tolerance:
        print("✓ 标准并行采样结果准确")
    else:
        print(f"✗ 标准并行采样结果不准确 (差异: {results['max_diff_standard']:.2e})")
    
    if results['max_diff_advanced'] < tolerance:
        print("✓ 高级并行采样结果准确")
    else:
        print(f"✗ 高级并行采样结果不准确 (差异: {results['max_diff_advanced']:.2e})")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
