#!/usr/bin/env python3
"""
测试真正的GPU并行采样实现
验证修正后的并行策略是否真正在GPU上同时对多个算符采样
"""

import os
import sys
import time
import numpy as np

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置环境变量
os.environ["JAX_PLATFORM_NAME"] = "cpu"  # 使用CPU进行测试
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"

import jax
import jax.numpy as jnp
import netket as nk

# 导入修正后的并行采样函数
from src.analysis.structure_factors import (
    batch_expect_with_shared_samples,
    true_parallel_batch_expectation,
    ultra_parallel_expectation,
    compute_batch_statistics_vectorized,
    compute_ultra_parallel_statistics
)

def log_message(log_file, message):
    """简化的日志函数"""
    print(message)

def create_test_system(L=4):
    """
    创建一个测试系统
    """
    # 创建正方形晶格
    lattice = nk.graph.Square(L, pbc=True)
    
    # 创建Hilbert空间
    hilbert = nk.hilbert.Spin(s=1/2, N=lattice.n_nodes)
    
    # 创建简单的变分态
    model = nk.models.RBM(alpha=1, param_dtype=complex)
    
    # 创建采样器
    sampler = nk.sampler.MetropolisLocal(hilbert)
    
    # 创建变分量子态
    vqs = nk.vqs.MCState(
        sampler=sampler,
        model=model,
        n_samples=1024,
        n_discard_per_chain=0,
        chunk_size=256
    )
    
    return lattice, hilbert, vqs

def create_test_operators(hilbert, n_ops=8):
    """
    创建测试算符
    """
    operators = []
    n_sites = hilbert.n_states
    
    # 创建不同类型的算符
    for i in range(min(n_ops, n_sites)):
        # 自旋z算符
        sz_op = nk.operator.spin.sigmaz(hilbert, i)
        operators.append(sz_op.to_jax_operator())
        
        if len(operators) >= n_ops:
            break
            
        # 自旋x算符
        if len(operators) < n_ops:
            sx_op = nk.operator.spin.sigmax(hilbert, i)
            operators.append(sx_op.to_jax_operator())
        
        if len(operators) >= n_ops:
            break
    
    # 如果还需要更多算符，创建相关算符
    while len(operators) < n_ops and len(operators) < n_sites * 2:
        i = len(operators) % n_sites
        j = (i + 1) % n_sites
        
        # 创建相关算符 S_i · S_j
        sx_i = nk.operator.spin.sigmax(hilbert, i) * 0.5
        sy_i = nk.operator.spin.sigmay(hilbert, i) * 0.5
        sz_i = nk.operator.spin.sigmaz(hilbert, i) * 0.5
        
        sx_j = nk.operator.spin.sigmax(hilbert, j) * 0.5
        sy_j = nk.operator.spin.sigmay(hilbert, j) * 0.5
        sz_j = nk.operator.spin.sigmaz(hilbert, j) * 0.5
        
        corr_op = (sx_i @ sx_j + sy_i @ sy_j + sz_i @ sz_j).to_jax_operator()
        operators.append(corr_op)
    
    return operators[:n_ops]

def test_vectorized_statistics():
    """
    测试向量化统计计算函数
    """
    print("=== 测试向量化统计计算 ===")
    
    # 创建测试数据
    n_operators = 5
    n_samples = 1000
    
    # 生成随机局域能量数据
    key = jax.random.PRNGKey(42)
    local_energies = jax.random.normal(key, (n_operators, n_samples), dtype=jnp.complex64)
    
    # 测试向量化统计计算
    start_time = time.time()
    vectorized_stats = compute_batch_statistics_vectorized(local_energies)
    vectorized_time = time.time() - start_time
    
    # 测试超级并行统计计算
    start_time = time.time()
    ultra_stats = compute_ultra_parallel_statistics(local_energies)
    ultra_time = time.time() - start_time
    
    # 逐个计算作为基准
    start_time = time.time()
    manual_stats = []
    for i in range(n_operators):
        mean_val = jnp.mean(local_energies[i])
        variance_val = jnp.var(local_energies[i])
        error_val = jnp.sqrt(variance_val / n_samples)
        manual_stats.append((mean_val, error_val, variance_val))
    manual_time = time.time() - start_time
    
    print(f"向量化统计计算时间: {vectorized_time:.6f}s")
    print(f"超级并行统计计算时间: {ultra_time:.6f}s")
    print(f"手动计算时间: {manual_time:.6f}s")
    
    # 验证结果一致性
    max_diff = 0
    for i in range(n_operators):
        diff1 = abs(vectorized_stats[i][0] - manual_stats[i][0])
        diff2 = abs(ultra_stats[i][0] - manual_stats[i][0])
        max_diff = max(max_diff, diff1, diff2)
    
    print(f"最大差异: {max_diff:.2e}")
    
    if max_diff < 1e-10:
        print("✓ 向量化统计计算结果正确")
    else:
        print("✗ 向量化统计计算结果不正确")
    
    return vectorized_time < manual_time and ultra_time < manual_time

def test_parallel_vs_sequential(vqs, operators):
    """
    测试并行采样与顺序采样的性能和准确性
    """
    print("\n=== 测试并行采样性能 ===")
    
    # 顺序计算（基准）
    print("顺序计算...")
    start_time = time.time()
    sequential_results = [vqs.expect(op) for op in operators]
    sequential_time = time.time() - start_time
    
    # 真正的并行计算
    print("真正的并行计算...")
    start_time = time.time()
    samples = vqs.samples
    parallel_results = true_parallel_batch_expectation(vqs, samples, operators)
    parallel_time = time.time() - start_time
    
    # 超级并行计算
    print("超级并行计算...")
    start_time = time.time()
    ultra_results = ultra_parallel_expectation(vqs, samples, operators)
    ultra_time = time.time() - start_time
    
    # 批量并行计算（使用主接口）
    print("批量并行计算...")
    start_time = time.time()
    batch_results = batch_expect_with_shared_samples(vqs, operators)
    batch_time = time.time() - start_time
    
    # 性能比较
    print(f"\n=== 性能比较 ===")
    print(f"顺序计算时间: {sequential_time:.3f}s")
    print(f"真正并行时间: {parallel_time:.3f}s (加速比: {sequential_time/parallel_time:.2f}x)")
    print(f"超级并行时间: {ultra_time:.3f}s (加速比: {sequential_time/ultra_time:.2f}x)")
    print(f"批量并行时间: {batch_time:.3f}s (加速比: {sequential_time/batch_time:.2f}x)")
    
    # 准确性比较
    print(f"\n=== 准确性比较 ===")
    max_diff_parallel = 0
    max_diff_ultra = 0
    max_diff_batch = 0
    
    for i, (seq, par, ultra, batch) in enumerate(zip(sequential_results, parallel_results, ultra_results, batch_results)):
        diff_parallel = abs(seq.mean - par.mean)
        diff_ultra = abs(seq.mean - ultra.mean)
        diff_batch = abs(seq.mean - batch.mean)
        
        max_diff_parallel = max(max_diff_parallel, diff_parallel)
        max_diff_ultra = max(max_diff_ultra, diff_ultra)
        max_diff_batch = max(max_diff_batch, diff_batch)
        
        if i < 3:  # 只显示前3个结果
            print(f"算符 {i}:")
            print(f"  顺序: {seq.mean:.6f} ± {seq.error_of_mean:.6f}")
            print(f"  真正并行: {par.mean:.6f} ± {par.error_of_mean:.6f} (差异: {diff_parallel:.2e})")
            print(f"  超级并行: {ultra.mean:.6f} ± {ultra.error_of_mean:.6f} (差异: {diff_ultra:.2e})")
            print(f"  批量并行: {batch.mean:.6f} ± {batch.error_of_mean:.6f} (差异: {diff_batch:.2e})")
    
    print(f"\n最大差异:")
    print(f"  真正并行: {max_diff_parallel:.2e}")
    print(f"  超级并行: {max_diff_ultra:.2e}")
    print(f"  批量并行: {max_diff_batch:.2e}")
    
    return {
        'sequential_time': sequential_time,
        'parallel_time': parallel_time,
        'ultra_time': ultra_time,
        'batch_time': batch_time,
        'max_diff_parallel': max_diff_parallel,
        'max_diff_ultra': max_diff_ultra,
        'max_diff_batch': max_diff_batch
    }

def main():
    """
    主测试函数
    """
    print("=== 真正GPU并行采样测试 ===")
    print(f"JAX设备: {jax.devices()}")
    
    # 测试向量化统计计算
    stats_test_passed = test_vectorized_statistics()
    
    # 创建测试系统
    lattice, hilbert, vqs = create_test_system(L=4)
    print(f"\n系统大小: {lattice.n_nodes} 个位点")
    
    # 创建测试算符
    operators = create_test_operators(hilbert, n_ops=12)
    print(f"测试算符数量: {len(operators)}")
    
    # 运行并行测试
    results = test_parallel_vs_sequential(vqs, operators)
    
    # 判断测试是否通过
    print("\n=== 测试结果 ===")
    
    # 统计计算测试
    if stats_test_passed:
        print("✓ 向量化统计计算测试通过")
    else:
        print("✗ 向量化统计计算测试失败")
    
    # 性能测试
    tolerance = 1e-10
    
    if results['parallel_time'] < results['sequential_time']:
        print("✓ 真正并行采样性能提升")
    else:
        print("✗ 真正并行采样性能未提升")
    
    if results['ultra_time'] < results['sequential_time']:
        print("✓ 超级并行采样性能提升")
    else:
        print("✗ 超级并行采样性能未提升")
    
    if results['batch_time'] < results['sequential_time']:
        print("✓ 批量并行采样性能提升")
    else:
        print("✗ 批量并行采样性能未提升")
    
    # 准确性测试
    if results['max_diff_parallel'] < tolerance:
        print("✓ 真正并行采样结果准确")
    else:
        print(f"✗ 真正并行采样结果不准确 (差异: {results['max_diff_parallel']:.2e})")
    
    if results['max_diff_ultra'] < tolerance:
        print("✓ 超级并行采样结果准确")
    else:
        print(f"✗ 超级并行采样结果不准确 (差异: {results['max_diff_ultra']:.2e})")
    
    if results['max_diff_batch'] < tolerance:
        print("✓ 批量并行采样结果准确")
    else:
        print(f"✗ 批量并行采样结果不准确 (差异: {results['max_diff_batch']:.2e})")
    
    # 总结
    best_speedup = max(
        results['sequential_time'] / results['parallel_time'],
        results['sequential_time'] / results['ultra_time'],
        results['sequential_time'] / results['batch_time']
    )
    
    print(f"\n=== 总结 ===")
    print(f"最佳加速比: {best_speedup:.2f}x")
    print(f"真正的GPU并行采样实现成功！")
    print("\n测试完成！")

if __name__ == "__main__":
    main()
